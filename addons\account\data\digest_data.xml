<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data noupdate="1">
        <record id="digest.digest_digest_default" model="digest.digest">
            <field name="kpi_account_total_revenue">True</field>
        </record>
    </data>

    <data>
        <record id="digest_tip_account_0" model="digest.tip">
            <field name="name">Tip: No need to print, put in an envelop and post your invoices</field>
            <field name="sequence">700</field>
            <field name="group_id" ref="account.group_account_invoice" />
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: No need to print, put in an envelop and post your invoices</p>
    <p class="tip_content">Use the “<i>Send by Post</i>” option to post invoices automatically. For the cost of a local stamp, we do all the manual work: your invoice will be printed in the right country, put in an envelop and sent by snail mail. Use this feature from the list view to post hundreds of invoices in bulk.</p>
    <img src="https://download.odoocdn.com/digests/account/static/src/img/invoice-stamps.png" class="illustration_border" />
</div>
            </field>
        </record>
    </data>
</odoo>
