<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
  <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
  <cbc:ID>FAC/2023/00052</cbc:ID>
  <cbc:IssueDate>2023-08-04</cbc:IssueDate>
  <cbc:DueDate>2023-09-04</cbc:DueDate>
  <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
  <cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
  <cac:OrderReference>
    <cbc:ID>FAC/2023/00052</cbc:ID>
    <cbc:SalesOrderID>S00012</cbc:SalesOrderID>
  </cac:OrderReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>FAC_2023_00052.pdf</cbc:ID>
    <cac:Attachment>
    </cac:Attachment>
  </cac:AdditionalDocumentReference>
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="9938">LU25587702</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>ALD Automotive LU</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>270 rte d'Arlon</cbc:StreetName>
        <cbc:CityName>Strassen</cbc:CityName>
        <cbc:PostalZone>8010</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>LU</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>LU12977109</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>ALD Automotive LU</cbc:RegistrationName>
        <cbc:CompanyID>LU12977109</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>ALD Automotive LU</cbc:Name>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="9938">LU25587702</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>Odoo Lu</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Rue de l'industrie 13</cbc:StreetName>
        <cbc:CityName>Windhof</cbc:CityName>
        <cac:Country>
          <cbc:IdentificationCode>LU</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>LU25587702</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Odoo Lu</cbc:RegistrationName>
        <cbc:CompanyID>LU25587702</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>Odoo Lu</cbc:Name>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:Delivery>
    <cac:DeliveryLocation>
      <cac:Address>
        <cbc:StreetName>Rue de l'industrie 13</cbc:StreetName>
        <cbc:CityName>Windhof</cbc:CityName>
        <cac:Country>
          <cbc:IdentificationCode>LU</cbc:IdentificationCode>
        </cac:Country>
      </cac:Address>
    </cac:DeliveryLocation>
  </cac:Delivery>
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
    <cbc:PaymentID>FAC/2023/00052</cbc:PaymentID>
    <cac:PayeeFinancialAccount>
      <cbc:ID>********************</cbc:ID>
    </cac:PayeeFinancialAccount>
  </cac:PaymentMeans>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="EUR">100.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="EUR">100.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="EUR">100.00</cbc:TaxInclusiveAmount>
    <cbc:PrepaidAmount currencyID="EUR">0.00</cbc:PrepaidAmount>
    <cbc:PayableAmount currencyID="EUR">100.00</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:InvoiceLine>
    <cbc:ID>1</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="EUR">100.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Description>Locations et leasing opérationnel - Véhicule HG6542</cbc:Description>
      <cbc:Name>Locations et leasing opérationnel</cbc:Name>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="EUR">100.00</cbc:PriceAmount>
    </cac:Price>
  </cac:InvoiceLine>
</Invoice>
