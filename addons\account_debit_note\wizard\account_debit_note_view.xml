<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_account_debit_note" model="ir.ui.view">
            <field name="name">account.debit.note.form</field>
            <field name="model">account.debit.note</field>
            <field name="arch" type="xml">
                <form string="Create Debit Note">
                    <field name="move_type" invisible="1"/>
                    <field name="journal_type" invisible="1"/>
                    <field name="move_ids" invisible="1"/>
                    <group>
                         <group>
                             <field name="reason"/>
                             <field name="date" string="Debit Note Date"/>
                             <field name="copy_lines" attrs="{'invisible': [('move_type', 'in', ['in_refund', 'out_refund'])]}"/>
                         </group>
                         <group>
                             <field name="journal_id" domain="[('type', '=', journal_type)]"/>
                         </group>
                    </group>
                    <footer>
                        <button string='Create Debit Note' name="create_debit" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
               </form>
            </field>
        </record>

        <record id="action_view_account_move_debit" model="ir.actions.act_window">
            <field name="name">Create Debit Note</field>
            <field name="res_model">account.debit.note</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_account_debit_note"/>
            <field name="target">new</field>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="binding_view_types">list</field>
        </record>
    </data>
</odoo>
