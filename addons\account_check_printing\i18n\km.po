# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_check_printing
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Lux <PERSON>k <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Lux <PERSON>k <<EMAIL>>, 2023\n"
"Language-Team: Khmer (https://app.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid " : Check Number Sequence"
msgstr "ពិនិត្យមើតួលេខស្នើរ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "<span>&amp;nbsp;</span>"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_left
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_right
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid ""
"Adjust the margins of generated checks to make it fit your printer's "
"settings."
msgstr ""
"កែតម្រូវរឹមនៃការត្រួតពិនិត្យដែលបានបង្កើតដើម្បីឱ្យវាសមនឹងការកំណត់នៃម៉ាស៊ីនបោះពុម្ពរបស់អ្នក។"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_amount_in_words
msgid "Amount in Words"
msgstr "ចំនួនពាក្យ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Cancel"
msgstr "លុបចោល"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid "Check Layout"
msgstr "ពិនិត្យមើលប្លង់"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_left
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_left
msgid "Check Left Margin"
msgstr "ការពិនិត្យមើលរឹមតួអក្សរខាងឆ្វេង"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_number
msgid "Check Number"
msgstr "ការពិនិត្យមើលតួលេខ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_journal_form_inherited
msgid "Check Printing"
msgstr "ពិនិត្យមើលកំពុងព្រីនចេញ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_right
msgid "Check Right Margin"
msgstr "ការពិនិត្យមើលរឹមខាងស្តាំ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_sequence_id
msgid "Check Sequence"
msgstr "ការពិនិត្យមើលផលបូកស្មើគ្នា"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_top
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_margin_top
msgid "Check Top Margin"
msgstr "ការពិនិត្យមើលរឹមបន្ទាប់ខាងលើ"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Check numbers can only consist of digits"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""
"ត្រូវពិនិត្យមើលការជម្រើសនេះប្រសិនបើការត្រួតពិនិត្យដែលបានបោះពុម្ពមុនរបស់អ្នកមិនត្រូវបានបង់លេខរៀង។"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Check to print"
msgstr "ពិនិត្យមើលការបោះពុម្ព"

#. module: account_check_printing
#: model:account.payment.method,name:account_check_printing.account_payment_method_check
msgid "Checks"
msgstr "ការត្រួតពិនិត្យ"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_sequence_id
msgid "Checks numbering sequence."
msgstr "ពិនិត្យមើលលេខស្មើរ"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_payment_check_printing_search
#, python-format
msgid "Checks to Print"
msgstr "ពិនិត្យមើលការបោះពុម្ព"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.account_journal_dashboard_kanban_view_inherited
msgid "Checks to print"
msgstr "ពិនិត្យមើលការបោះពុម្ពចេញ"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_config_settings
msgid "Config Settings"
msgstr "កំណត់រចនាសម្ព័ន្ធ"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_res_partner
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "ចូលទៅផ្ទាំងការតំឡើង"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__id
msgid "ID"
msgstr "អត្តសញ្ញាណ"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"In order to print multiple checks at once, they must belong to the same bank"
" journal."
msgstr ""
"ក្នុងគោលបំណងដើម្បីបោះពុម្ពការត្រួតពិនិត្យច្រើនក្នុងពេលតែមួយពួកគេត្រូវតែស្ថិតនៅក្នុងទស្សនាវដ្តីធនាគារដូចគ្នា។"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_journal
msgid "Journal"
msgstr "ទិនានុប្បវត្តិ"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_move
msgid "Journal Entry"
msgstr "ការចុះទិន្នានុប្បរត្ត"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_manual_sequencing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__check_manual_sequencing
msgid "Manual Numbering"
msgstr "លេខរៀង"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid "Multi-Pages Check Stub"
msgstr "ពិនិត្យពហុទំព័រ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_journal__check_next_number
#: model:ir.model.fields,field_description:account_check_printing.field_print_prenumbered_checks__next_check_number
msgid "Next Check Number"
msgstr "លេខត្រួតពិនិត្យបន្ទាប់"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#: code:addons/account_check_printing/wizard/print_prenumbered_checks.py:0
#, python-format
msgid "Next Check Number should only contains numbers."
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields.selection,name:account_check_printing.selection__res_company__account_check_printing_layout__disabled
msgid "None"
msgstr "គ្មាន"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__payment_method_line_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_res_users__property_payment_method_id
msgid "Payment Method"
msgstr "យុទ្ឋសាស្រ្តបង់ប្រាក់"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_method
msgid "Payment Methods"
msgstr ""
"វិធី​ទូទាត់លុយ\n"
" \n"
" "

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment
msgid "Payments"
msgstr "ការបង់ប្រាក់"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Payments to print as a checks must have 'Check' selected as payment method "
"and not have already been reconciled"
msgstr ""
"ការទូទាត់ដើម្បីបោះពុម្ពដូចជាការត្រួតពិនិត្យមួយត្រូវតែមាន 'ពិនិត្យ' "
"ដែលបានជ្រើសជាវិធីសាស្រ្តទូទាត់និងមិនត្រូវបានគេផ្សះផ្សាគ្នារួចទៅហើយ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"Please enter the number of the first pre-printed check that you are about to"
" print on."
msgstr "សូមបញ្ចូលលេខនៃការពិនិត្យដែលបានបោះពុម្ពដំបូងដែលអ្នកត្រៀមនឹងបោះពុម្ព។"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_bank_statement_line__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_move__preferred_payment_method_id
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__preferred_payment_method_id
msgid "Preferred Payment Method"
msgstr ""

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_partner__property_payment_method_id
#: model:ir.model.fields,help:account_check_printing.field_res_users__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid "Print"
msgstr "បោះពុម្ព"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Print Check"
msgstr "ការត្រួតពិនិត្យការបោះពុម្ព"

#. module: account_check_printing
#: model:ir.actions.server,name:account_check_printing.action_account_print_checks
msgid "Print Checks"
msgstr "ការពិនិត្យមើលការបោះពុម្ពចេញ"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,field_description:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid "Print Date Label"
msgstr "ការពិនិត្យមើលកាលបរិច្ឆេទនិងទម្រង់"

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#: model:ir.model,name:account_check_printing.model_print_prenumbered_checks
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
#, python-format
msgid "Print Pre-numbered Checks"
msgstr "ការពិនិត្យមើលលើលេខដែលត្រួវបោះពុម្ព"

#. module: account_check_printing
#: model:ir.model,name:account_check_printing.model_account_payment_register
msgid "Register Payment"
msgstr "ការចុះឈ្មោះការបង់ប្រាក់"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_res_company__account_check_printing_margin_right
msgid "Right Margin"
msgstr "រឹមខាងស្តំា"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_layout
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_layout
msgid ""
"Select the format corresponding to the check paper you will be printing your checks on.\n"
"In order to disable the printing feature, select 'None'."
msgstr ""
"ជ្រើសទ្រង់ទ្រាយដែលត្រូវគ្នានឹងក្រដាសពិនិត្យដែលអ្នកនឹងបោះពុម្ពការត្រួតពិនិត្យរបស់អ្នក។"
" ដើម្បីបិទលក្ខណៈពិសេសការបោះពុម្ពសូមជ្រើស 'គ្មាន' ។"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Sent"
msgstr "បានផ្ងើរ"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_journal__check_next_number
msgid "Sequence number of the next printed check."
msgstr "ចំនួនលំដាប់នៃការពិនិត្យបោះពុម្ពបន្ទាប់។"

#. module: account_check_printing
#: model:ir.model.fields,field_description:account_check_printing.field_account_payment__show_check_number
msgid "Show Check Number"
msgstr ""

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"Something went wrong with Check Layout, please select another layout in "
"Invoicing/Accounting Settings and try again."
msgstr ""

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"The following numbers are already used:\n"
"%s"
msgstr ""

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_journal.py:0
#, python-format
msgid ""
"The last check number was %s. In order to avoid a check being rejected by "
"the bank, you can only use a greater number."
msgstr ""
"លេខត្រួតពិនិត្យចុងក្រោយគឺ%sដើម្បីជៀសវាងការត្រួតពិនិត្យដែលត្រូវបានបដិសេធដោយធនាគារអ្នកអាចប្រើបានតែលេខធំជាង។"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_account_payment__check_number
msgid ""
"The selected journal is configured to print check numbers. If your pre-"
"printed check paper already has numbers or if the current numbering is "
"wrong, you can change it in the journal configuration page."
msgstr ""
"ទិនានុប្បវត្តិដែលបានជ្រើសត្រូវបានកំណត់រចនាសម្ព័ន្ធដើម្បីបោះពុម្ពលេខត្រួតពិនិត្យ។"
" "
"ប្រសិនបើក្រដាសពិនិត្យមុនរបស់អ្នកមានលេខឬលេខរៀងបច្ចុប្បន្នអ្នកអាចប្តូរវានៅក្នុងទំព័រកំណត់រចនាសម្ព័ន្ធទិនានុប្បវត្តិ។"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_multi_stub
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_multi_stub
msgid ""
"This option allows you to print check details (stub) on multiple pages if "
"they don't fit on a single page."
msgstr ""
"ជម្រើសនេះអនុញ្ញាតឱ្យអ្នកបោះពុម្ពពត៌មានលំអិតត្រួតពិនិត្យ (stub) "
"នៅលើទំព័រច្រើនប្រសិនបើវាមិនសមនឹងទំព័រតែមួយ។"

#. module: account_check_printing
#: model:ir.model.fields,help:account_check_printing.field_res_company__account_check_printing_date_label
#: model:ir.model.fields,help:account_check_printing.field_res_config_settings__account_check_printing_date_label
msgid ""
"This option allows you to print the date label on the check as per CPA.\n"
"Disable this if your pre-printed check includes the date label."
msgstr ""

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.print_pre_numbered_checks_view
msgid ""
"This will allow to save on payments the number of the corresponding check."
msgstr ""
"នេះនឹងអនុញ្ញាតឱ្យសន្សំលើចំនួនការទូទាត់តាមចំនួននៃការពិនិត្យដែលត្រូវគ្នា។"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Unmark Sent"
msgstr "សញ្ញាសម្គាល់មិនបានផ្ញើ"

#. module: account_check_printing
#: model_terms:ir.ui.view,arch_db:account_check_printing.view_account_payment_form_inherited
msgid "Void Check"
msgstr ""

#. module: account_check_printing
#. odoo-python
#: code:addons/account_check_printing/models/account_payment.py:0
#, python-format
msgid ""
"You have to choose a check layout. For this, go in Invoicing/Accounting "
"Settings, search for 'Checks layout' and set one."
msgstr ""
