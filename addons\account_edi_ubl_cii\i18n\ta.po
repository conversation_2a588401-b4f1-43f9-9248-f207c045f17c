# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_ubl_cii
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Language-Team: Tamil (https://app.transifex.com/odoo/teams/41243/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_facturx_export_22
msgid "42"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "<strong>Format used to import the invoice: %s</strong>"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid ""
"<strong>Format used to import the invoice: %s</strong> <p><li> %s </li></p>"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "A payment of %s was detected."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_a_nz
msgid "A-NZ BIS Billing 3.0"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Articles 226 items 11 to 15 Directive 2006/112/EN"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "At least one of the following fields %s is required on %s."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_de
msgid "BIS3 DE (XRechnung)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_common
msgid ""
"Common functions for EDI documents: generate the data, the constraints, etc"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid "Conditional cash/payment discount"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency ?"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Could not retrieve the tax: %s %% for line '%s'."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Could not retrieve the unit of measure for line with label '%s'."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Down Payment"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Down Payments"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_efff
msgid "E-FFF (BE)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "Each invoice line shall have one and only one tax."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "Each invoice line should have a product or a label."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Each invoice line should have at least one tax."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_mail_template
msgid "Email Templates"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_format.py:0
#, python-format
msgid ""
"Errors occured while creating the EDI document (format: %s). The receiver "
"might refuse it."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Export outside the EU"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_cii
msgid "Factur-x/XRechnung CII 2.2.0"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"For intracommunity supply, the actual delivery date or the invoicing period "
"should be included."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "For intracommunity supply, the delivery address should be included."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Intra-Community supply"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "No Electronic Address Scheme (EAS) could be found for %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid ""
"No gross price, net price nor line subtotal amount found for line in xml"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_sg
msgid "SG BIS Billing 3.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_nl
msgid "SI-UBL 2.0 (NLCIUS)"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Tax '%s' is invalid: %s"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"The VAT number of the supplier does not seem to be valid. It should be of "
"the form: NO179728982MVA."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The VAT of the %s should be prefixed with its country code."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The country is required for the %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid "The currency '%s' is not active."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The customer %s must have a Bronnoysund company registry."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The customer %s must have a KVK or OIN number."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "The element %s is required on %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "The field %s is required on %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"The field 'Sanitized Account Number' is required on the Recipient Bank."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"The invoice contains line(s) with a negative unit price, which is not "
"allowed. You might need to set a negative quantity instead."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid ""
"The invoice has been converted into a credit note and the quantities have "
"been reverted."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_format.py:0
#, python-format
msgid ""
"The journal in which to upload should either be a sale or a purchase "
"journal."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The supplier %s must have a Bronnoysund company registry."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The supplier %s must have a KVK or OIN number."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_20
msgid "UBL 2.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_21
msgid "UBL 2.1"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_bis3
msgid "UBL BIS Billing 3.0.12"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"You should include at least one tax per invoice line. [BR-CO-04]-Each "
"Invoice line (BG-25) shall be categorized with an Invoiced item VAT category"
" code (BT-151)."
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_line_facturx_export_22
msgid "false"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr ""
