<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="account_edi.action_open_payment_edi_documents" model="ir.actions.act_window">
            <field name="name">Electronic invoicing</field>
            <field name="res_model">account.edi.document</field>
            <field name="view_mode">tree</field>
            <field name="domain">[('move_id.payment_id', '=', active_id), ('error', '!=', False)]</field>
        </record>

        <record id="view_payment_form_inherit" model="ir.ui.view">
            <field name="name">account.payment.form.inherit</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form" />
            <field name="arch" type="xml">
                <xpath expr="//header" position="after">
                    <field name="edi_blocking_level" invisible="1" />
                    <field name="edi_error_count" invisible="1" />
                    <div class="alert alert-info" role="alert" style="margin-bottom:0px;"
                         attrs="{'invisible': ['|', ('edi_web_services_to_process', 'in', ['', False]), ('state', '=', 'draft')]}">
                         <div>The payment will be processed asynchronously by the following E-invoicing service :
                            <field name="edi_web_services_to_process" class="oe_inline"/>
                         </div>
                         <button name="action_process_edi_web_services" type="object" class="oe_link" string="Process now"/>
                    </div>
                    <div class="alert alert-danger" role="alert" style="margin-bottom:0px;"
                        attrs="{'invisible': ['|', ('edi_error_count', '=', 0), ('edi_blocking_level', '!=', 'error')]}">
                        <div class="o_row">
                            <field name="edi_error_message" />
                            <button name="%(account_edi.action_open_edi_documents)d" string="⇒ See errors" type="action" class="oe_link" attrs="{'invisible': [('edi_error_count', '=', 1)]}" /> 
                            <button name="action_retry_edi_documents_error" type="object" class="oe_link oe_inline" string="Retry" />
                        </div>
                    </div>
                    <div class="alert alert-warning" role="alert" style="margin-bottom:0px;"
                        attrs="{'invisible': ['|', ('edi_error_count', '=', 0), ('edi_blocking_level', '!=', 'warning')]}">
                        <div class="o_row">
                            <field name="edi_error_message" />
                            <button name="%(account_edi.action_open_edi_documents)d" string="⇒ See errors" type="action" class="oe_link" attrs="{'invisible': [('edi_error_count', '=', 1)]}" /> 
                        </div>
                    </div>
                    <div class="alert alert-info" role="alert" style="margin-bottom:0px;"
                        attrs="{'invisible': ['|', ('edi_error_count', '=', 0), ('edi_blocking_level', '!=', 'info')]}">
                        <div class="o_row">
                            <field name="edi_error_message" />
                            <button name="%(account_edi.action_open_edi_documents)d" string="⇒ See errors" type="action" class="oe_link" attrs="{'invisible': [('edi_error_count', '=', 1)]}" /> 
                        </div>
                    </div>
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="after">
                    <field name="edi_state" attrs="{'invisible': ['|', ('edi_state', '=', False), ('state', '=', 'draft')]}"/>
                </xpath>
                <xpath expr="//group[@name='group2']" position="after">
                    <group groups="base.group_no_one">
                        <field name="edi_document_ids"
                               string="EDI Documents"
                               groups="base.group_no_one"
                               options="{'no_open': '1'}"
                               attrs="{'invisible': [('edi_document_ids', '=', [])]}">
                            <tree create="false" delete="false" edit="false" decoration-danger="error">
                                <field name="name"/>
                                <field name="edi_format_name"/>
                                <field name="state"/>
                                <field name="error" invisible="1"/>
                                <field name="blocking_level" invisible="1"/>
                                <button name="action_export_xml"
                                        type="object"
                                        class="oe_link oe_inline"
                                        string="Download"
                                        groups="base.group_no_one"
                                        attrs="{'invisible': ['|', ('error', '=', False), ('blocking_level', '=', 'info')]}"/>
                            </tree>
                        </field>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
