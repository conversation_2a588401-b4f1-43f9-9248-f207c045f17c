# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_proxy_client
# 
# Translators:
# hish, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:51+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_res_company__account_edi_proxy_client_ids
msgid "Account Edi Proxy Client"
msgstr ""

#. module: account_edi_proxy_client
#: model_terms:ir.ui.view,arch_db:account_edi_proxy_client.view_form_account_edi_proxy_client_user
msgid "Account Journal"
msgstr "Санхүүгийн журнал"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__active
msgid "Active"
msgstr "Идэвхтэй"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_format_code
msgid "Code"
msgstr "Дансны код"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__company_id
msgid "Company"
msgstr "Компани"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: account_edi_proxy_client
#: model:ir.actions.act_window,name:account_edi_proxy_client.action_tree_account_edi_proxy_client_user
msgid "EDI Proxy User"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.ui.menu,name:account_edi_proxy_client.menu_account_proxy_client_user
msgid "EDI Proxy Users"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_format_id
msgid "Edi Format"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "Edi Identification"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id
msgid "ID"
msgstr "ID"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id_client
msgid "Id Client"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key
msgid "Private Key"
msgstr "Хувийн түлхүүр"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_filename
msgid "Private Key Filename"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__refresh_token
msgid "Refresh Token"
msgstr "Тасалбарыг сэргээх"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key
msgid "The key to encrypt all the user's data"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid ""
"The unique id that identifies this user for on the edi format, typically the"
" vat"
msgstr ""

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s"
msgstr ""

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s. %s"
msgstr ""

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service tried to contact does not exist. The url was %r"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_edi_identification_per_format
msgid "This edi identification is already assigned to a user"
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_id_client
msgid "This id_client is already used on another user."
msgstr ""
