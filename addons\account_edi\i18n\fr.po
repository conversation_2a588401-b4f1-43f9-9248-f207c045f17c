# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing error(s)"
msgstr "Erreur(s) de facturation électronique"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing info(s)"
msgstr "Informations de facturation électronique"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing warning(s)"
msgstr "Avertissement(s) sur la facturation électronique"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A cancellation of the EDI has been requested."
msgstr "Une annulation de l'EDI a été demandée."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A request for cancellation of the EDI has been called off."
msgstr "Une demande d'annulation de l'EDI a été révoquée."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "Pièce jointe"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "Niveau de blocage"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"Bloque l'opération en cours du document en fonction de la gravité de l'erreur :\n"
"* Info : le document n'est pas bloqué et tout fonctionne comme il se doit.\n"
"* Attention : il y a une erreur qui n'empêche pas l'opération de facturation électronique en cours de réussir.\n"
"* Erreur : il y a une erreur qui bloque l'opération de facturation électronique en cours."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "Abandonner la demande d'annulation de l'EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "Annulé"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
#, python-format
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"Impossible de désactiver (%s) sur ce journal car tous les documents ne sont "
"pas synchronisés"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "Code"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "Compatible Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "Créé le"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid "Display the currency"
msgstr "Afficher la devise"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Download"
msgstr "Télécharger"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
#: model:ir.cron,cron_name:account_edi.ir_cron_edi_network
msgid "EDI : Perform web services operations"
msgstr "EDI : Réaliser des opérations de services web"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "EDI Documents"
msgstr "Documents EDI"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "Format EDI"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "Format EDI qui prend en charge les mouvements dans ce journal"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "Niveau de blocage EDI"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "Contenu EDI"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_document_ids
msgid "Edi Document"
msgstr "Document Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_count
msgid "Edi Error Count"
msgstr "Nombre d'erreurs Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_message
msgid "Edi Error Message"
msgstr "Message d'erreur Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "Format Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "Edi afficher le bouton Ignorer l'annulation"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "Edi afficher le bouton d'annulation"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "Services web Edi à traiter"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_journal_form_inherited
msgid "Electronic Data Interchange"
msgstr "Échange de données informatisé"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "Document électronique pour un account.move"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.actions.act_window,name:account_edi.action_open_payment_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_state
msgid "Electronic invoicing"
msgstr "Facturation électronique"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "Traitement de la facturation électronique nécessaire"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "État de la facturation électronique"

#. module: account_edi
#: model:ir.model,name:account_edi.model_mail_template
msgid "Email Templates"
msgstr "Modèles d'e-mail"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "Erreur"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "Format du nom"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_error_count
msgid "How many EDIs are in error for this move ?"
msgstr "Combien d'EDIs sont en statut d'erreur pour ce mouvement ?"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "ID"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "Info"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"Configuration de la facture invalide :\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "Mouvement"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "Nom"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "Seulement un document edi par mouvement par format"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_payment
msgid "Payments"
msgstr "Paiements"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Process now"
msgstr "Traiter maintenant"

#. module: account_edi
#: model:ir.model,name:account_edi.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unité de mesure de produit"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "Refaire la séquence des pièces comptables."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "Action de rapport"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "Demande d'annulation EDI"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Retry"
msgstr "Réessayer"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "Envoyer des factures XML/EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "Envoyé"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "État"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr "L'état agrégé de tous les EDI avec le service web de ce mouvement."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"The currency (%s) of the document you are uploading is not active in this database.\n"
"Please activate it and update the currency rate if needed before trying again to import."
msgstr ""
"La devise (%s) du document que vous téléchargez n'est pas active dans cette base de données.\n"
"Veuillez l'activer et mettre à jour le taux de change si nécessaire avant de recommencer l'import."

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""
"Le fichier généré par edi_format_id lorsque les factures sont publiées (et "
"ce document est traité)."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
#, python-format
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr ""
"Les documents suivants ont déjà été envoyés et ne peuvent pas être "
"réordonnés : %s"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"The invoice will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""
"La facture sera traitée de manière asynchrone par le service de facturation "
"électronique suivant :"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid ""
"The payment will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""
"Le paiement sera traité de manière asynchrone par le service de facturation "
"électronique suivant :"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""
"Le test de la dernière erreur qui s'est produite lors de l'opération de "
"facturation électronique."

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "Ce code existe déjà"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
#, python-format
msgid "This document is being sent by another process already. "
msgstr "Ce document est déjà envoyé via un autre processus. "

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "À annuler"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "À envoyer"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "Avertissement"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""
"Vous ne pouvez modifier la pièce comptable suivante %s car un document "
"électronique a déjà été envoyé. Veuillez plutôt utiliser le bouton 'Demande "
"d'annulation EDI'."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
#, python-format
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""
"Vous ne pouvez pas délier une pièce jointe qui est un document EDI envoyé au"
" gouvernement."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "⇒ See errors"
msgstr "⇒ Voir les erreurs"
