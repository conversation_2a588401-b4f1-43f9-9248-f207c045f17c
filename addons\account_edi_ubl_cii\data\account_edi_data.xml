<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="edi_facturx_1_0_05" model="account.edi.format">
        <field name="name">Factur-X (PDF/A-3)</field>
        <field name="code">facturx_1_0_05</field>
    </record>

    <record id="edi_nlcius_1" model="account.edi.format">
        <field name="name">NLCIUS (Netherlands)</field>
        <field name="code">nlcius_1</field>
    </record>

    <record id="ubl_bis3" model="account.edi.format">
        <field name="name">Peppol BIS Billing 3.0</field>
        <field name="code">ubl_bis3</field>
    </record>

    <record id="ubl_de" model="account.edi.format">
        <field name="name">XRechnung UBL (Germany)</field>
        <field name="code">ubl_de</field>
    </record>

    <record id="edi_ubl_2_1" model="account.edi.format">
        <field name="name">UBL 2.1</field>
        <field name="code">ubl_2_1</field>
    </record>

    <record id="edi_efff_1" model="account.edi.format">
        <field name="name">E-FFF (BE)</field>
        <field name="code">efff_1</field>
    </record>

    <record id="ubl_a_nz" model="account.edi.format">
        <field name="name">A-NZ BIS Billing 3.0</field>
        <field name="code">ubl_a_nz</field>
    </record>

    <record id="ubl_sg" model="account.edi.format">
        <field name="name">SG BIS Billing 3.0</field>
        <field name="code">ubl_sg</field>
    </record>
</odoo>
