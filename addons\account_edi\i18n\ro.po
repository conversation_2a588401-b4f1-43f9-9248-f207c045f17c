# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>_nexterp, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing error(s)"
msgstr " Eroare(e) de facturare electronică"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing info(s)"
msgstr " Informații de facturare electronică"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing warning(s)"
msgstr " Avertisment(e) privind facturarea electronică"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A cancellation of the EDI has been requested."
msgstr "A fost solicitată anularea EDI."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A request for cancellation of the EDI has been called off."
msgstr "O cerere de anulare a EDI a fost anulată."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "Atașament"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "Nivel de blocare"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"Blochează funcționarea curentă a documentului în funcție de gravitatea erorii:\n"
"  * Info: documentul nu este blocat și totul funcționează așa cum ar trebui.\n"
"  * Atenție: există o eroare care nu împiedică reușita operațiunii curente de facturare electronică.\n"
"  * Eroare: există o eroare care blochează operațiunea curentă de Facturare Electronică."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "Anulați anularea EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "Anulat"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
#, python-format
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"Nu se poate dezactiva (%s) în acest jurnal deoarece nu toate documentele "
"sunt sincronizate"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "Cod"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "Edi Compatibil"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "Creat în"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid "Display the currency"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Download"
msgstr "Descarcă"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
#: model:ir.cron,cron_name:account_edi.ir_cron_edi_network
msgid "EDI : Perform web services operations"
msgstr "EDI: Efectuați operațiuni de servicii web"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "EDI Documents"
msgstr "Documente EDI"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "Format EDI"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "Format EDI care acceptă mișcări în acest jurnal"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "Nivel de blocare Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "Conținut Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_document_ids
msgid "Edi Document"
msgstr "Document Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_count
msgid "Edi Error Count"
msgstr "Nr. Erori Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_message
msgid "Edi Error Message"
msgstr "Mesaj de eroare Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "Format Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "Afișare buton de anulare abandonat Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "Afișare Buton Anulare Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "Servicii Web Edi de Procesat"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_journal_form_inherited
msgid "Electronic Data Interchange"
msgstr "Schimb Electronic de Date"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "Document electronic pentru account.move"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.actions.act_window,name:account_edi.action_open_payment_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_state
msgid "Electronic invoicing"
msgstr "Facturare Electronică"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "Este necesară procesarea facturii electronice"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "Starea facturării electronice"

#. module: account_edi
#: model:ir.model,name:account_edi.model_mail_template
msgid "Email Templates"
msgstr "Șabloane e-mail"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "Eroare"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "Nume Format"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_error_count
msgid "How many EDIs are in error for this move ?"
msgstr "Câți EDI sunt în eroare pentru această mișcare?"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "ID"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "Informații"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"Configurație nevalidă a facturii:\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "Jurnal"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "Notă contabilă"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_line
msgid "Journal Item"
msgstr "Element jurnal"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "Mișcare"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "Nume"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "Un singur document edi după mutare după format"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_payment
msgid "Payments"
msgstr "Plăți"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Process now"
msgstr "Procesați acum"

#. module: account_edi
#: model:ir.model,name:account_edi.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unitate de măsură produs"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "Refaceți secvența de intrări în jurnal."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "Acțiune raport"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "Solicitare Anulare EDI"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Retry"
msgstr "Reîncearcă"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "Trimiteți facturi XML / EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "Trimis"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "Stare"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr ""
"Starea agregată a tuturor EDI-urilor cu serviciul web al acestei mișcări"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"The currency (%s) of the document you are uploading is not active in this database.\n"
"Please activate it and update the currency rate if needed before trying again to import."
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""
"Fișierul generat de edi_format_id atunci când factura este postată (și acest"
" document este procesat)."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
#, python-format
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr ""
"Următoarele documente au fost deja trimise și nu pot fi reordonate: %s"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"The invoice will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""
"Factura va fi procesată asincron de următorul serviciu de facturare "
"electronică:"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid ""
"The payment will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""
"Textul ultimei erori care a avut loc în timpul operațiunii cu factură "
"electronică."

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "Acest cod există deja"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
#, python-format
msgid "This document is being sent by another process already. "
msgstr "Acest document este deja trimis printr-un alt proces. "

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "Pentru Anulare"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "Pentru trimitere"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "Atenție"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""
"Nu puteți edita următoarea intrare în jurnal %s deoarece un document "
"electronic a fost deja trimis. Vă rugăm să utilizați butonul 'Request EDI "
"Cancellation' în schimb."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
#, python-format
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""
"Nu puteți deconecta un atașament care este un document EDI trimis "
"guvernului."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "⇒ See errors"
msgstr "⇒ Vizualizare erori"
