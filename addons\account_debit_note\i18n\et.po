# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_debit_note
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>eli Õigus <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
# Anna, 2024
# Birgit Vijar, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_move_form_debit
msgid "<span class=\"o_stat_text\">Debit Notes</span>"
msgstr "<span class=\"o_stat_text\">Deebet</span>"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_move_form_debit
msgid "Add Debit Note"
msgstr ""

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr "Lisa deebetarve viisard"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Cancel"
msgstr "Tühista"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__copy_lines
msgid "Copy Lines"
msgstr "Kopeeri read"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__country_code
msgid "Country Code"
msgstr "Riigikood"

#. module: account_debit_note
#: model:ir.actions.act_window,name:account_debit_note.action_view_account_move_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Create Debit Note"
msgstr "Loo kreeditarve"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__date
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Debit Note Date"
msgstr "Arve kuupäev"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/models/account_move.py:0
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_ids
#: model:ir.model.fields,field_description:account_debit_note.field_account_payment__debit_note_ids
#, python-format
msgid "Debit Notes"
msgstr "Deebet"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__id
msgid "ID"
msgstr "ID"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__journal_id
msgid "If empty, uses the journal of the journal entry to be debited."
msgstr "Kui tühi, kasutab andmiku kandes andmikku, mida debiteeritakse."

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__copy_lines
msgid ""
"In case you need to do corrections for every line, it can be in handy to "
"copy them.  We won't copy them for debit notes from credit notes. "
msgstr ""
"Paranduste tegemisel on mugavam kopeerida ridu, Ridu ei kopeerida "
"kreeditarvetelt deebetarvetele. "

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_type
msgid "Journal Type"
msgstr "Andmiku tüüp"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_ids
msgid "Move"
msgstr "Kanne"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_type
msgid "Move Type"
msgstr "Liigutamise viis"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_count
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_count
#: model:ir.model.fields,field_description:account_debit_note.field_account_payment__debit_note_count
msgid "Number of Debit Notes"
msgstr "Deebetarvete arv"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_origin_id
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_origin_id
#: model:ir.model.fields,field_description:account_debit_note.field_account_payment__debit_origin_id
msgid "Original Invoice Debited"
msgstr "Algne arve debiteeritud"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__reason
msgid "Reason"
msgstr "Põhjus"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO riigi kood kahe tähega. \n"
"Saad kasutada seda välja kiirotsinguks"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,help:account_debit_note.field_account_move__debit_note_ids
#: model:ir.model.fields,help:account_debit_note.field_account_payment__debit_note_ids
msgid "The debit notes created for this invoice"
msgstr "Deebetarved, mis on loodud sellele arvele"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#, python-format
msgid "This debit note was created from: %s"
msgstr "See deebetarve loodi: %s"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_id
msgid "Use Specific Journal"
msgstr "Kasuta kindlat andmikku"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#, python-format
msgid "You can only debit posted moves."
msgstr "Debiteerida saab ainult postitatud kandeid. "
