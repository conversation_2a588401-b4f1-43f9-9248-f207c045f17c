# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# Wil <PERSON>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing error(s)"
msgstr "Fout(en) elektronische facturering"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing info(s)"
msgstr "Info(s) elektronische facturering"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing warning(s)"
msgstr "Waarschuwing(en) elektronische facturering"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A cancellation of the EDI has been requested."
msgstr "Er is verzocht om annulering van de EDI."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A request for cancellation of the EDI has been called off."
msgstr "Een verzoek tot opheffing van de EDI is afgeblazen."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "Bijlage"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "Blokkeerniveau"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"Blokkeert de huidige documentbewerking afhankelijk van de ernst van de fout:\n"
"   * Info: het document is niet geblokkeerd en alles werkt zoals het hoort.\n"
"   * Waarschuwing: er is een fout opgetreden die het succes van de huidige elektronische facturering niet verhindert.\n"
"   * Fout: er is een fout opgetreden die de huidige elektronische facturering blokkeert."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "Opzeggen EDI Annulering"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
#, python-format
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"Kan (%s) niet deactiveren in dit dagboek omdat niet alle documenten worden "
"gesynchroniseerd"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "Code"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "Compatible Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "Gemaakt door"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "Gemaakt op"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "Weergavenaam"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid "Display the currency"
msgstr "Toon valuta"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Download"
msgstr "Downloaden"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
#: model:ir.cron,cron_name:account_edi.ir_cron_edi_network
msgid "EDI : Perform web services operations"
msgstr "EDI: voer webservicebewerkingen uit"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "EDI Documents"
msgstr "EDI Documenten"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "EDI formaat"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "EDI format that dat wordt ondersteund in dit dagboek"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "EDI blokkeer-niveau"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "Edi inhoud"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_document_ids
msgid "Edi Document"
msgstr "EDI Document"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_count
msgid "Edi Error Count"
msgstr "Aantal EDI fouten"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_message
msgid "Edi Error Message"
msgstr "EDI foutmelding"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "EDI formaat"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "EDI Toon Verlaten Annuleren-knop"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "EDI, Toon annulleerknop"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "Edi Web Services om te verwerken"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_journal_form_inherited
msgid "Electronic Data Interchange"
msgstr "Electronic Data Interchange"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "Elektronisch document voor een boeking"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.actions.act_window,name:account_edi.action_open_payment_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_state
msgid "Electronic invoicing"
msgstr "Elektronische facturatie"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "Elektronische factuurverwerking nodig"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "Status elektronische facturering"

#. module: account_edi
#: model:ir.model,name:account_edi.model_mail_template
msgid "Email Templates"
msgstr "E-mailsjablonen"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "Fout"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "Soort"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_error_count
msgid "How many EDIs are in error for this move ?"
msgstr "Hoeveel EDI's zijn fout bij deze boeking?"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "ID"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "Info"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"Ongeldige factuurconfiguratie:\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "Dagboek"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "Laatst geupdate op"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "Verplaatsing"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "Naam"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "Slechts één edi-document per formaat"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_payment
msgid "Payments"
msgstr "Betalingen"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Process now"
msgstr "Verwerk nu"

#. module: account_edi
#: model:ir.model,name:account_edi.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Maateenheid product"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "Hernummer een reeks voor dit dagboek."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "Rapport actie"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "Verzoek EDI-annulering"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Retry"
msgstr "Opnieuw proberen"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "Verstuur XML/EDI facturen"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "Verzonden"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "Provincie / Staat"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr ""
"De geaggregeerde status van alle EDI’s met webservice van deze verplaatsing"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"The currency (%s) of the document you are uploading is not active in this database.\n"
"Please activate it and update the currency rate if needed before trying again to import."
msgstr ""
"De valuta (%s) van het document dat je importeert is niet actief in deze database.\n"
"Activeer deze voordat je opnieuw probeert te importeren."

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""
"Het bestand gegenereerd door edi_format_id wanneer de factuur wordt geboekt "
"(en dit document wordt verwerkt)."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
#, python-format
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr ""
"De volgende documenten zijn al verzonden en kunnen niet opnieuw worden "
"gerangschikt: %s"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"The invoice will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""
"De factuur wordt asynchroon verwerkt door de volgende E-facturatiedienst:"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid ""
"The payment will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""
"De betaling wordt asynchroon verwerkt door de volgende e-factureringsdienst:"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""
"De tekst van de laatste fout die is opgetreden tijdens de elektronische "
"factuurbewerking."

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "Deze code bestaat al"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
#, python-format
msgid "This document is being sent by another process already. "
msgstr "Dit document wordt al door een ander proces verzonden."

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "Te annuleren"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "Te verzenden"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "Waarschuwing"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""
"Je kunt de volgende boeking %s niet bewerken omdat er al een elektronisch "
"document is verzonden. Gebruik in plaats daarvan de knop 'EDI-annulering "
"aanvragen'."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
#, python-format
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""
"Je kunt een bijlage niet ontkoppelen als een EDI-document dat naar de "
"overheid is verzonden."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "⇒ See errors"
msgstr "⇒ Bekijk fouten"
