# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing error(s)"
msgstr "เกิดข้อผิดพลาดเกี่ยวกับใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing info(s)"
msgstr "ข้อมูลการออกใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid " Electronic invoicing warning(s)"
msgstr "คำเตือนเกี่ยวกับใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A cancellation of the EDI has been requested."
msgstr "มีการร้องขอให้ยกเลิก EDI"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid "A request for cancellation of the EDI has been called off."
msgstr "คำขอยกเลิก EDI ถูกยกเลิก"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "การแนบ"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "ระดับการปิดกั้น"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"ปิดกั้นการดำเนินการปัจจุบันของเอกสารขึ้นอยู่กับความรุนแรงของข้อผิดพลาด:\n"
"* ข้อมูล: เอกสารไม่ถูกปิดกั้นและทุกอย่างทำงานได้ตามปกติ\n"
"* คำเตือน: มีข้อผิดพลาดที่ไม่ได้ขัดขวางการดำเนินการออกใบแจ้งหนี้อิเล็กทรอนิกส์ปัจจุบันให้สำเร็จ\n"
"* ข้อผิดพลาด: มีข้อผิดพลาดที่ขัดขวางการดำเนินการออกใบแจ้งหนี้อิเล็กทรอนิกส์ปัจจุบัน"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "ปิดการยกเลิก EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "ยกเลิก"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
#, python-format
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"ไม่สามารถปิดการใช้งาน (%s) ในสมุดรายวันนี้ได้ "
"เนื่องจากเอกสารไม่ครบทั้งหมดจะถูกซิงโครไนซ์"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "โค้ด"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "Edi ที่เข้ากันได้"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid "Display the currency"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
#: model:ir.cron,cron_name:account_edi.ir_cron_edi_network
msgid "EDI : Perform web services operations"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "EDI Documents"
msgstr "เอกสาร EDI"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "รูปแบบ EDI"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "รูปแบบ EDI ที่รองรับการเคลื่อนไหวในสมุดรายวันนี้"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "ระดับการปิดกั้น Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "เนื้อหา Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_document_ids
msgid "Edi Document"
msgstr "เอกสาร Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_count
msgid "Edi Error Count"
msgstr "จำนวนข้อผิดพลาดของ Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_error_message
msgid "Edi Error Message"
msgstr "ข้อความแสดงข้อผิดพลาดของ Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "รูปแบบ Edi"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "Edi แสดงปุ่มยกเลิกที่ถูกละทิ้ง"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "Edi แสดงปุ่มยกเลิก"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "Edi บริการเว็บที่จะประมวลผล"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_journal_form_inherited
msgid "Electronic Data Interchange"
msgstr "การแลกเปลี่ยนข้อมูลทางอิเล็กทรอนิกส์"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "เอกสารอิเล็กทรอนิกส์สำหรับ account.move"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.actions.act_window,name:account_edi.action_open_payment_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_payment__edi_state
msgid "Electronic invoicing"
msgstr "การออกใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "จำเป็นต้องมีการประมวลผลใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "สถานะการออกใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#: model:ir.model,name:account_edi.model_mail_template
msgid "Email Templates"
msgstr "เทมเพลตอีเมล"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "ผิดพลาด"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "ชื่อรูปแบบ"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_error_count
msgid "How many EDIs are in error for this move ?"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "ไอดี"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "ข้อมูล"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"การกำหนดค่าใบแจ้งหนี้ไม่ถูกต้อง:\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "สมุดบันทึก"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกประจำวัน"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document____last_update
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "ย้าย"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "ชื่อ"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "มีเอกสาร edi เพียงฉบับเดียวโดยย้ายตามรูปแบบ"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_payment
msgid "Payments"
msgstr "การชำระเงิน"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Process now"
msgstr "ดำเนินการทันที"

#. module: account_edi
#: model:ir.model,name:account_edi.model_uom_uom
msgid "Product Unit of Measure"
msgstr "หน่วยวัดสินค้า"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "สร้างลำดับของรายการสมุดรายวันใหม่"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "การดําเนินการรายงาน"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "ขอยกเลิก EDI"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "Retry"
msgstr "ลองใหม่"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "ส่งใบแจ้งหนี้ XML/EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "รัฐ"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
#: model:ir.model.fields,help:account_edi.field_account_payment__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr "สถานะรวมของ EDI ทั้งหมดพร้อมบริการบนเว็บของการย้ายครั้งนี้"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"The currency (%s) of the document you are uploading is not active in this database.\n"
"Please activate it and update the currency rate if needed before trying again to import."
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""
"ไฟล์ที่สร้างโดย edi_format_id เมื่อมีการผ่านรายการใบแจ้งหนี้ "
"(และเอกสารนี้ได้รับการประมวลผล)"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
#, python-format
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr "เอกสารต่อไปนี้ได้ถูกส่งไปแล้วและไม่สามารถจัดลำดับใหม่ได้:%s"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"The invoice will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid ""
"The payment will be processed asynchronously by the following E-invoicing "
"service :"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""
"ข้อความของข้อผิดพลาดล่าสุดที่เกิดขึ้นระหว่างการดำเนินการใบแจ้งหนี้อิเล็กทรอนิกส์"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "รหัสนี้มีอยู่แล้ว"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
#, python-format
msgid "This document is being sent by another process already. "
msgstr "เอกสารนี้กำลังถูกส่งโดยขั้นตอนอื่นอยู่แล้ว"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "ยกเลิก"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "ส่ง"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "คำเตือน"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
#, python-format
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""
"คุณไม่สามารถแก้ไขรายการสมุดรายวันต่อไปนี้ %s ได้ "
"เนื่องจากมีการส่งเอกสารอิเล็กทรอนิกส์ไปแล้ว โปรดใช้ปุ่ม 'ขอยกเลิก EDI' แทน"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
#, python-format
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""
"คุณไม่สามารถยกเลิกการเชื่อมโยงเอกสารแนบที่เป็นเอกสาร EDI ที่ส่งถึงรัฐบาลได้"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
#: model_terms:ir.ui.view,arch_db:account_edi.view_payment_form_inherit
msgid "⇒ See errors"
msgstr "⇒ ดูข้อผิดพลาด"
